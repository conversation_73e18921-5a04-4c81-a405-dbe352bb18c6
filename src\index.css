@tailwind base;
@tailwind components;
@tailwind utilities;

/* Currency formatting styles for Excel-like table display */
.currency-cell {
  font-family: 'Courier New', monospace;
  text-align: right;
}

.currency-symbol {
  float: left;
}

.currency-amount {
  text-align: right;
}

/* Ensure proper spacing in currency cells */
.currency-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

/* Table cell alignment utilities */
.table-cell-currency {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

.table-cell-number {
  text-align: right;
  font-variant-numeric: tabular-nums;
}

.table-cell-text {
  text-align: left;
}

/* Monospace font for better number alignment */
.monospace-numbers {
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Courier New', monospace;
  font-variant-numeric: tabular-nums;
}

/* Custom scrollbar styles */
@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-track-gray-800 {
    scrollbar-color: #374151 #1f2937;
  }
  
  .scrollbar-thumb-gray-600 {
    scrollbar-color: #4b5563 #1f2937;
  }
  
  /* Webkit scrollbar styles */
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
  
  .scrollbar-track-gray-800::-webkit-scrollbar-track {
    background: #1f2937;
    border-radius: 3px;
  }
  
  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background: #4b5563;
    border-radius: 3px;
  }
  
  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb:hover {
    background: #6b7280;
  }
}

/* Dark mode specific styles */
@media (prefers-color-scheme: dark) {
  :root {
    color-scheme: dark;
  }
}

/* Ensure proper contrast in dark mode */
.dark {
  color-scheme: dark;
}

/* Custom focus styles for better accessibility */
.dark *:focus {
  outline-color: #3b82f6;
}