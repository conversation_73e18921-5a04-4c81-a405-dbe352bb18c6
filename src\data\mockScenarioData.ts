// Column definitions for the scenario table
export const ScenarioTableColumns = {
  POLICY_YEAR: 'Policy Year',
  END_OF_AGE: 'End of Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_OUTLAY: 'Net Outlay',
  NET_SURRENDER_VALUE: 'Net Surrender Value',
  NET_DEATH_BENEFIT: 'Net Death Benefit'
} as const;

// Column configurations with additional display properties
export const ScenarioTableColumnConfig = [
  { 
    key: 'policyYear',
    header: ScenarioTableColumns.POLICY_YEAR,
    width: 100
  },
  { 
    key: 'endOfAge',
    header: ScenarioTableColumns.END_OF_AGE,
    width: 100
  },
  { 
    key: 'plannedPremium',
    header: ScenarioTableColumns.PLANNED_PREMIUM,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netOutlay',
    header: ScenarioTableColumns.NET_OUTLAY,
    width: 150,
    isCurrency: true
  },
  { 
    key: 'netSurrenderValue',
    header: ScenarioTableColumns.NET_SURRENDER_VALUE,
    width: 180,
    isCurrency: true
  },
  { 
    key: 'netDeathBenefit',
    header: ScenarioTableColumns.NET_DEATH_BENEFIT,
    width: 180,
    isCurrency: true
  }
];

export interface ScenarioTableData {
  policyYear: number;
  endOfAge: number;
  plannedPremium: number;
  netOutlay: number;
  netSurrenderValue: number;
  netDeathBenefit: number;
}

// ===== CASH VALUE ANALYSIS TABLE =====
export const As_Is_Loan = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const AsIsLoan = [
  { key: 'policyYear', header: As_Is_Loan.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: As_Is_Loan.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: As_Is_Loan.AGE, width: 80 },
  { key: 'plannedPremium', header: As_Is_Loan.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: As_Is_Loan.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: As_Is_Loan.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: As_Is_Loan.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: As_Is_Loan.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: As_Is_Loan.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: As_Is_Loan.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: As_Is_Loan.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: As_Is_Loan.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: As_Is_Loan.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: As_Is_Loan.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: As_Is_Loan.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: As_Is_Loan.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface AsIsLoanTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

// ===== DEATH BENEFIT ANALYSIS TABLE =====
export const Face_Amount = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const FaceAmount = [
  { key: 'policyYear', header: Face_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Face_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Face_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Face_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Face_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Face_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Face_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Face_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Face_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Face_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Face_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Face_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Face_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Face_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Face_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Face_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface FaceAmountTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

export const Face_Amount_Varies_By_Year = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const FaceAmountVariesByYear = [
  { key: 'policyYear', header: Face_Amount_Varies_By_Year.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Face_Amount_Varies_By_Year.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Face_Amount_Varies_By_Year.AGE, width: 80 },
  { key: 'plannedPremium', header: Face_Amount_Varies_By_Year.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Face_Amount_Varies_By_Year.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Face_Amount_Varies_By_Year.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Face_Amount_Varies_By_Year.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Face_Amount_Varies_By_Year.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Face_Amount_Varies_By_Year.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Face_Amount_Varies_By_Year.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Face_Amount_Varies_By_Year.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Face_Amount_Varies_By_Year.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Face_Amount_Varies_By_Year.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Face_Amount_Varies_By_Year.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Face_Amount_Varies_By_Year.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Face_Amount_Varies_By_Year.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface FaceAmountVariesByYearTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

export const Premium_Amount = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const PremiumAmount = [
  { key: 'policyYear', header: Premium_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Premium_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Premium_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Premium_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Premium_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Premium_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Premium_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Premium_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Premium_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Premium_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Premium_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Premium_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Premium_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Premium_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Premium_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Premium_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface PremiumAmountTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

export const Stop_Premium_Amount = {
  POLICY_YEAR: 'Policy Year',
  CALENDAR_YEAR: 'Calender Year',
  AGE: 'Age',
  PLANNED_PREMIUM: 'Planned Premium',
  NET_VALUE_BEGINNING_OF_YEAR: 'Net Value - Beginning of Year',
  INTEREST_RATE: 'Interest Rate',
  INTEREST_AMOUNT: 'Interest Amount',
  FACE_AMOUNT: 'Face Amount',
  WITHDRAWAL: 'Withdrawal',
  POLICY_LOAN: 'Policy Loan',
  LOAN_INTEREST_RATE: 'Loan Interest Rate',
  LOAN_INTEREST: 'Loan Interest',
  LOAN_REPAYMENT: 'Loan Repayment',
  LOAN_OUTSTANDING: 'Loan Outstanding',
  CHARGES: 'Charges',
  NET_CASH_VALUE: 'Net Cash Value'
} as const;

export const StopPremiumAmount = [
  { key: 'policyYear', header: Stop_Premium_Amount.POLICY_YEAR, width: 120 },
  { key: 'calendarYear', header: Stop_Premium_Amount.CALENDAR_YEAR, width: 120 },
  { key: 'age', header: Stop_Premium_Amount.AGE, width: 80 },
  { key: 'plannedPremium', header: Stop_Premium_Amount.PLANNED_PREMIUM, width: 140, isCurrency: true },
  { key: 'netValueBeginningOfYear', header: Stop_Premium_Amount.NET_VALUE_BEGINNING_OF_YEAR, width: 180, isCurrency: true },
  { key: 'interestRate', header: Stop_Premium_Amount.INTEREST_RATE, width: 140 },
  { key: 'interestAmount', header: Stop_Premium_Amount.INTEREST_AMOUNT, width: 140, isCurrency: true },
  { key: 'faceAmount', header: Stop_Premium_Amount.FACE_AMOUNT, width: 140, isCurrency: true },
  { key: 'withdrawal', header: Stop_Premium_Amount.WITHDRAWAL, width: 120, isCurrency: true },
  { key: 'policyLoan', header: Stop_Premium_Amount.POLICY_LOAN, width: 140, isCurrency: true },
  { key: 'loanInterestRate', header: Stop_Premium_Amount.LOAN_INTEREST_RATE, width: 140 },
  { key: 'loanInterest', header: Stop_Premium_Amount.LOAN_INTEREST, width: 140, isCurrency: true },
  { key: 'loanRepayment', header: Stop_Premium_Amount.LOAN_REPAYMENT, width: 140, isCurrency: true },
  { key: 'loanOutstanding', header: Stop_Premium_Amount.LOAN_OUTSTANDING, width: 140, isCurrency: true },
  { key: 'charges', header: Stop_Premium_Amount.CHARGES, width: 120, isCurrency: true },
  { key: 'netCashValue', header: Stop_Premium_Amount.NET_CASH_VALUE, width: 140, isCurrency: true }
];

export interface StopPremiumAmountTableData {
  policyYear: number;
  calendarYear: number;
  age: number;
  plannedPremium: number;
  netValueBeginningOfYear: number;
  interestRate: number;
  interestAmount: number;
  faceAmount: number;
  withdrawal: number;
  policyLoan: number;
  loanInterestRate: number;
  loanInterest: number;
  loanRepayment: number;
  loanOutstanding: number;
  charges: number;
  netCashValue: number;
}

export const generateMockTableData = (scenario: any): ScenarioTableData[] => {
  console.log('📊 Generating table data for scenario:', scenario.id, scenario.name);

  // Generate mock table data for 10 years (2025-2034)
  const tableData: ScenarioTableData[] = [
    {
      policyYear: 1,
      endOfAge: 40,
      plannedPremium: 10000,
      netOutlay: 5000,
      netSurrenderValue: 50000,
      netDeathBenefit: 250000
    },
    {
      policyYear: 2,
      endOfAge: 41,
      plannedPremium: 10000,
      netOutlay: 5500,
      netSurrenderValue: 51000,
      netDeathBenefit: 255000
    },
    {
      policyYear: 3,
      endOfAge: 42,
      plannedPremium: 10000,
      netOutlay: 6000,
      netSurrenderValue: 52000,
      netDeathBenefit: 260000
    },
    {
      policyYear: 4,
      endOfAge: 43,
      plannedPremium: 10000,
      netOutlay: 6500,
      netSurrenderValue: 53000,
      netDeathBenefit: 265000
    },
    {
      policyYear: 5,
      endOfAge: 44,
      plannedPremium: 10000,
      netOutlay: 7000,
      netSurrenderValue: 54000,
      netDeathBenefit: 270000
    },
    {
      policyYear: 6,
      endOfAge: 45,
      plannedPremium: 10000,
      netOutlay: 7500,
      netSurrenderValue: 55000,
      netDeathBenefit: 275000
    },
    {
      policyYear: 7,
      endOfAge: 46,
      plannedPremium: 10000,
      netOutlay: 8000,
      netSurrenderValue: 56000,
      netDeathBenefit: 280000
    },
    {
      policyYear: 8,
      endOfAge: 47,
      plannedPremium: 10000,
      netOutlay: 8500,
      netSurrenderValue: 57000,
      netDeathBenefit: 285000
    },
    {
      policyYear: 9,
      endOfAge: 48,
      plannedPremium: 10000,
      netOutlay: 9000,
      netSurrenderValue: 58000,
      netDeathBenefit: 290000
    },
    {
      policyYear: 10,
      endOfAge: 49,
      plannedPremium: 10000,
      netOutlay: 9500,
      netSurrenderValue: 59000,
      netDeathBenefit: 295000
    }
  ];

  console.log('✅ Generated table data:', tableData.length, 'rows');
  return tableData;
};

// ===== GENERATE MOCK DATA FOR ALL TABLES =====

export const generateAsIsLoanTableData = (): AsIsLoanTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 2500,
      netValueBeginningOfYear: 12500,
      interestRate: 0.03,
      interestAmount: 375,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 525,
      loanRepayment: 600,
      loanOutstanding: 10425,
      charges: 100,
      netCashValue: 12550
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 2500,
      netValueBeginningOfYear: 15050,
      interestRate: 0.03,
      interestAmount: 451.5,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600,
      loanOutstanding: 10346.25,
      charges: 100,
      netCashValue: 15174.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 2500,
      netValueBeginningOfYear: 17674.25,
      interestRate: 0.03,
      interestAmount: 530.23,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600,
      loanOutstanding: 10263.56,
      charges: 100,
      netCashValue: 17875.17
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 2500,
      netValueBeginningOfYear: 20375.17,
      interestRate: 0.03,
      interestAmount: 611.26,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600,
      loanOutstanding: 10176.74,
      charges: 100,
      netCashValue: 20661.69
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 2500,
      netValueBeginningOfYear: 23161.69,
      interestRate: 0.03,
      interestAmount: 694.85,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600,
      loanOutstanding: 10085.58,
      charges: 100,
      netCashValue: 23531.12
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 45,
      plannedPremium: 2500,
      netValueBeginningOfYear: 26031.12,
      interestRate: 0.03,
      interestAmount: 780.93,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600,
      loanOutstanding: 9989.86,
      charges: 100,
      netCashValue: 26487.77
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 46,
      plannedPremium: 2500,
      netValueBeginningOfYear: 28987.77,
      interestRate: 0.03,
      interestAmount: 869.63,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600,
      loanOutstanding: 9889.35,
      charges: 100,
      netCashValue: 29532.56
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 47,
      plannedPremium: 2500,
      netValueBeginningOfYear: 32032.56,
      interestRate: 0.03,
      interestAmount: 960.98,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600,
      loanOutstanding: 9783.82,
      charges: 100,
      netCashValue: 32668.25
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 48,
      plannedPremium: 2500,
      netValueBeginningOfYear: 35168.25,
      interestRate: 0.03,
      interestAmount: 1055.05,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600,
      loanOutstanding: 9672.01,
      charges: 100,
      netCashValue: 35898.11
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 49,
      plannedPremium: 2500,
      netValueBeginningOfYear: 38398.11,
      interestRate: 0.03,
      interestAmount: 1151.94,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 483.60,
      loanRepayment: 600,
      loanOutstanding: 9553.61,
      charges: 100,
      netCashValue: 39225.84
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 50,
      plannedPremium: 2500,
      netValueBeginningOfYear: 41725.84,
      interestRate: 0.03,
      interestAmount: 1251.78,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 477.68,
      loanRepayment: 600,
      loanOutstanding: 9431.29,
      charges: 100,
      netCashValue: 42652.23
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 51,
      plannedPremium: 2500,
      netValueBeginningOfYear: 45152.23,
      interestRate: 0.03,
      interestAmount: 1354.57,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 471.56,
      loanRepayment: 600,
      loanOutstanding: 9302.85,
      charges: 100,
      netCashValue: 46178.39
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 52,
      plannedPremium: 2500,
      netValueBeginningOfYear: 48678.39,
      interestRate: 0.03,
      interestAmount: 1460.35,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 465.14,
      loanRepayment: 600,
      loanOutstanding: 9167.99,
      charges: 100,
      netCashValue: 49808.75
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 53,
      plannedPremium: 2500,
      netValueBeginningOfYear: 52308.75,
      interestRate: 0.03,
      interestAmount: 1569.26,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 458.40,
      loanRepayment: 600,
      loanOutstanding: 9026.39,
      charges: 100,
      netCashValue: 53550.22
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 54,
      plannedPremium: 2500,
      netValueBeginningOfYear: 56050.22,
      interestRate: 0.03,
      interestAmount: 1681.51,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 451.32,
      loanRepayment: 600,
      loanOutstanding: 8877.71,
      charges: 100,
      netCashValue: 57406.60
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 55,
      plannedPremium: 2500,
      netValueBeginningOfYear: 59906.60,
      interestRate: 0.03,
      interestAmount: 1797.20,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 443.89,
      loanRepayment: 600,
      loanOutstanding: 8721.60,
      charges: 100,
      netCashValue: 61381.31
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 56,
      plannedPremium: 2500,
      netValueBeginningOfYear: 63881.31,
      interestRate: 0.03,
      interestAmount: 1916.44,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 436.08,
      loanRepayment: 600,
      loanOutstanding: 8557.68,
      charges: 100,
      netCashValue: 65478.99
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 57,
      plannedPremium: 2500,
      netValueBeginningOfYear: 67978.99,
      interestRate: 0.03,
      interestAmount: 2039.37,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 427.88,
      loanRepayment: 600,
      loanOutstanding: 8385.56,
      charges: 100,
      netCashValue: 69703.92
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 58,
      plannedPremium: 2500,
      netValueBeginningOfYear: 72203.92,
      interestRate: 0.03,
      interestAmount: 2166.12,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 419.28,
      loanRepayment: 600,
      loanOutstanding: 8204.84,
      charges: 100,
      netCashValue: 74051.20
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 59,
      plannedPremium: 2500,
      netValueBeginningOfYear: 76551.20,
      interestRate: 0.03,
      interestAmount: 2296.54,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 410.24,
      loanRepayment: 600,
      loanOutstanding: 8015.08,
      charges: 100,
      netCashValue: 78533.02
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 60,
      plannedPremium: 2500,
      netValueBeginningOfYear: 81033.02,
      interestRate: 0.03,
      interestAmount: 2430.99,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 400.75,
      loanRepayment: 600,
      loanOutstanding: 7815.83,
      charges: 100,
      netCashValue: 83164.43
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 61,
      plannedPremium: 2500,
      netValueBeginningOfYear: 85664.43,
      interestRate: 0.03,
      interestAmount: 2569.93,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 390.79,
      loanRepayment: 600,
      loanOutstanding: 7606.62,
      charges: 100,
      netCashValue: 87924.75
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 62,
      plannedPremium: 2500,
      netValueBeginningOfYear: 90424.75,
      interestRate: 0.03,
      interestAmount: 2712.74,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 380.33,
      loanRepayment: 600,
      loanOutstanding: 7386.95,
      charges: 100,
      netCashValue: 92817.82
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 63,
      plannedPremium: 2500,
      netValueBeginningOfYear: 95317.82,
      interestRate: 0.03,
      interestAmount: 2859.53,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 369.35,
      loanRepayment: 600,
      loanOutstanding: 7156.30,
      charges: 100,
      netCashValue: 97847.70
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 64,
      plannedPremium: 2500,
      netValueBeginningOfYear: 100347.70,
      interestRate: 0.03,
      interestAmount: 3010.43,
      faceAmount: 350000,
      withdrawal: 0,
      policyLoan: 0,
      loanInterestRate: 0.05,
      loanInterest: 357.82,
      loanRepayment: 600,
      loanOutstanding: 6914.12,
      charges: 100,
      netCashValue: 103020.43
    }
  ];
};

export const generatePremiumAnalysisTableData = (): PremiumAmountTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 12500.00,
      interestRate: 0.03,
      interestAmount: 375.00,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.00,
      loanRepayment: 600.00,
      loanOutstanding: 10425.00,
      charges: 100.00,
      netCashValue: 13050.00
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 16050.00,
      interestRate: 0.03,
      interestAmount: 481.50,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.00,
      loanOutstanding: 10346.25,
      charges: 100.00,
      netCashValue: 16710.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 19710.25,
      interestRate: 0.03,
      interestAmount: 591.31,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.00,
      loanOutstanding: 10263.56,
      charges: 100.00,
      netCashValue: 20484.49
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 23484.49,
      interestRate: 0.03,
      interestAmount: 704.53,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600.00,
      loanOutstanding: 10176.74,
      charges: 100.00,
      netCashValue: 24375.84
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 27375.84,
      interestRate: 0.03,
      interestAmount: 821.28,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600.00,
      loanOutstanding: 10085.58,
      charges: 100.00,
      netCashValue: 28388.28
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 45,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 31388.28,
      interestRate: 0.03,
      interestAmount: 941.65,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600.00,
      loanOutstanding: 9989.86,
      charges: 100.00,
      netCashValue: 32525.65
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 46,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 35525.65,
      interestRate: 0.03,
      interestAmount: 1065.77,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600.00,
      loanOutstanding: 9889.35,
      charges: 100.00,
      netCashValue: 36791.93
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 47,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 39791.93,
      interestRate: 0.03,
      interestAmount: 1193.76,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600.00,
      loanOutstanding: 9783.82,
      charges: 100.00,
      netCashValue: 41291.22
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 48,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 44291.22,
      interestRate: 0.03,
      interestAmount: 1328.74,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600.00,
      loanOutstanding: 9672.01,
      charges: 100.00,
      netCashValue: 46009.77
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 49,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 49009.77,
      interestRate: 0.03,
      interestAmount: 1470.29,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 483.60,
      loanRepayment: 600.00,
      loanOutstanding: 9553.61,
      charges: 100.00,
      netCashValue: 50996.46
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 50,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 53996.46,
      interestRate: 0.03,
      interestAmount: 1619.89,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 477.68,
      loanRepayment: 600.00,
      loanOutstanding: 9431.29,
      charges: 100.00,
      netCashValue: 55138.67
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 51,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 58138.67,
      interestRate: 0.03,
      interestAmount: 1744.16,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 471.56,
      loanRepayment: 600.00,
      loanOutstanding: 9302.85,
      charges: 100.00,
      netCashValue: 60411.27
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 52,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 63411.27,
      interestRate: 0.03,
      interestAmount: 1902.34,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 465.14,
      loanRepayment: 600.00,
      loanOutstanding: 9167.99,
      charges: 100.00,
      netCashValue: 65848.75
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 53,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 68848.75,
      interestRate: 0.03,
      interestAmount: 2065.46,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 458.40,
      loanRepayment: 600.00,
      loanOutstanding: 9026.39,
      charges: 100.00,
      netCashValue: 71455.81
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 54,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 74455.81,
      interestRate: 0.03,
      interestAmount: 2233.67,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 451.32,
      loanRepayment: 600.00,
      loanOutstanding: 8877.71,
      charges: 100.00,
      netCashValue: 77238.16
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 55,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 80238.16,
      interestRate: 0.03,
      interestAmount: 2407.14,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 443.89,
      loanRepayment: 600.00,
      loanOutstanding: 8721.60,
      charges: 100.00,
      netCashValue: 83201.41
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 56,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 86201.41,
      interestRate: 0.03,
      interestAmount: 2586.04,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 436.08,
      loanRepayment: 600.00,
      loanOutstanding: 8557.68,
      charges: 100.00,
      netCashValue: 89351.37
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 57,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 92351.37,
      interestRate: 0.03,
      interestAmount: 2770.54,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 427.88,
      loanRepayment: 600.00,
      loanOutstanding: 8385.56,
      charges: 100.00,
      netCashValue: 96694.03
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 58,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 99694.03,
      interestRate: 0.03,
      interestAmount: 2990.82,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 419.28,
      loanRepayment: 600.00,
      loanOutstanding: 8204.84,
      charges: 100.00,
      netCashValue: 104265.57
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 59,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 107265.57,
      interestRate: 0.03,
      interestAmount: 3217.97,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 410.24,
      loanRepayment: 600.00,
      loanOutstanding: 8015.08,
      charges: 100.00,
      netCashValue: 113073.30
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 60,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 116073.30,
      interestRate: 0.03,
      interestAmount: 3482.20,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 400.75,
      loanRepayment: 600.00,
      loanOutstanding: 7815.83,
      charges: 100.00,
      netCashValue: 122154.75
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 61,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 125154.75,
      interestRate: 0.03,
      interestAmount: 3754.64,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 390.79,
      loanRepayment: 600.00,
      loanOutstanding: 7606.62,
      charges: 100.00,
      netCashValue: 131518.60
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 62,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 134518.60,
      interestRate: 0.03,
      interestAmount: 4035.56,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 380.33,
      loanRepayment: 600.00,
      loanOutstanding: 7386.95,
      charges: 100.00,
      netCashValue: 141173.83
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 63,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 144173.83,
      interestRate: 0.03,
      interestAmount: 4325.21,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 369.35,
      loanRepayment: 600.00,
      loanOutstanding: 7156.30,
      charges: 100.00,
      netCashValue: 151129.69
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 64,
      plannedPremium: 3000.00,
      netValueBeginningOfYear: 154129.69,
      interestRate: 0.03,
      interestAmount: 4623.89,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 357.82,
      loanRepayment: 600.00,
      loanOutstanding: 6914.12,
      charges: 100.00,
      netCashValue: 161395.76
    }
  ];
};

export const generateDeathBenefitTableData = (): FaceAmountTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 35,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 12500.0,
      interestRate: 0.03,
      interestAmount: 375.0,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.0,
      loanRepayment: 600.0,
      loanOutstanding: 10425.0,
      charges: 150.0,
      netCashValue: 12550.0
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 36,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 15050.0,
      interestRate: 0.03,
      interestAmount: 451.5,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.0,
      loanOutstanding: 10346.25,
      charges: 150.0,
      netCashValue: 15174.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 37,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 17674.25,
      interestRate: 0.03,
      interestAmount: 530.23,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.0,
      loanOutstanding: 10263.56,
      charges: 150.0,
      netCashValue: 17875.17
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 38,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 20375.17,
      interestRate: 0.03,
      interestAmount: 611.26,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600.0,
      loanOutstanding: 10176.74,
      charges: 150.0,
      netCashValue: 20661.69
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 39,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 23161.69,
      interestRate: 0.03,
      interestAmount: 694.85,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600.0,
      loanOutstanding: 10085.58,
      charges: 150.0,
      netCashValue: 23531.12
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 40,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 26031.12,
      interestRate: 0.03,
      interestAmount: 780.93,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600.0,
      loanOutstanding: 9989.86,
      charges: 150.0,
      netCashValue: 26487.77
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 41,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 28987.77,
      interestRate: 0.03,
      interestAmount: 869.63,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600.0,
      loanOutstanding: 9889.35,
      charges: 150.0,
      netCashValue: 29532.56
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 42,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 32032.56,
      interestRate: 0.03,
      interestAmount: 960.98,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600.0,
      loanOutstanding: 9783.82,
      charges: 150.0,
      netCashValue: 32668.25
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 43,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 35168.25,
      interestRate: 0.03,
      interestAmount: 1055.05,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600.0,
      loanOutstanding: 9672.01,
      charges: 150.0,
      netCashValue: 35898.11
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 44,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 38398.11,
      interestRate: 0.03,
      interestAmount: 1151.94,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 483.60,
      loanRepayment: 600.0,
      loanOutstanding: 9553.61,
      charges: 150.0,
      netCashValue: 39225.84
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 45,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 41725.84,
      interestRate: 0.03,
      interestAmount: 1251.78,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 477.68,
      loanRepayment: 600.0,
      loanOutstanding: 9431.29,
      charges: 150.0,
      netCashValue: 42502.23
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 46,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 45002.23,
      interestRate: 0.03,
      interestAmount: 1350.07,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 471.56,
      loanRepayment: 600.0,
      loanOutstanding: 9302.85,
      charges: 150.0,
      netCashValue: 45881.74
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 47,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 48381.74,
      interestRate: 0.03,
      interestAmount: 1451.45,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 465.14,
      loanRepayment: 600.0,
      loanOutstanding: 9167.99,
      charges: 150.0,
      netCashValue: 49548.05
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 48,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 52048.05,
      interestRate: 0.03,
      interestAmount: 1561.44,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 458.40,
      loanRepayment: 600.0,
      loanOutstanding: 9026.39,
      charges: 150.0,
      netCashValue: 53351.09
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 49,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 55851.09,
      interestRate: 0.03,
      interestAmount: 1675.53,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 451.32,
      loanRepayment: 600.0,
      loanOutstanding: 8877.71,
      charges: 150.0,
      netCashValue: 57225.30
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 50,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 59725.30,
      interestRate: 0.03,
      interestAmount: 1791.76,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 443.89,
      loanRepayment: 600.0,
      loanOutstanding: 8721.60,
      charges: 150.0,
      netCashValue: 61223.17
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 51,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 63723.17,
      interestRate: 0.03,
      interestAmount: 1911.70,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 436.08,
      loanRepayment: 600.0,
      loanOutstanding: 8557.68,
      charges: 150.0,
      netCashValue: 65348.79
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 52,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 67848.79,
      interestRate: 0.03,
      interestAmount: 2035.46,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 427.88,
      loanRepayment: 600.0,
      loanOutstanding: 8385.56,
      charges: 150.0,
      netCashValue: 69606.37
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 53,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 72106.37,
      interestRate: 0.03,
      interestAmount: 2163.19,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 419.28,
      loanRepayment: 600.0,
      loanOutstanding: 8204.84,
      charges: 150.0,
      netCashValue: 74000.28
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 54,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 76500.28,
      interestRate: 0.03,
      interestAmount: 2295.01,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 410.24,
      loanRepayment: 600.0,
      loanOutstanding: 8015.08,
      charges: 150.0,
      netCashValue: 78535.05
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 55,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 81035.05,
      interestRate: 0.03,
      interestAmount: 2431.05,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 400.75,
      loanRepayment: 600.0,
      loanOutstanding: 7815.83,
      charges: 150.0,
      netCashValue: 83216.35
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 56,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 85716.35,
      interestRate: 0.03,
      interestAmount: 2571.49,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 390.79,
      loanRepayment: 600.0,
      loanOutstanding: 7606.62,
      charges: 150.0,
      netCashValue: 88037.05
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 57,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 90537.05,
      interestRate: 0.03,
      interestAmount: 2716.11,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 380.33,
      loanRepayment: 600.0,
      loanOutstanding: 7386.95,
      charges: 150.0,
      netCashValue: 93002.83
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 58,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 95502.83,
      interestRate: 0.03,
      interestAmount: 2865.08,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 369.35,
      loanRepayment: 600.0,
      loanOutstanding: 7156.30,
      charges: 150.0,
      netCashValue: 98217.56
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 59,
      plannedPremium: 2500.0,
      netValueBeginningOfYear: 100717.56,
      interestRate: 0.03,
      interestAmount: 3021.53,
      faceAmount: 500000.0,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 357.82,
      loanRepayment: 600.0,
      loanOutstanding: 6914.12,
      charges: 150.0,
      netCashValue: 103581.17
    }
  ];
};

export const generatePolicyPerformanceTableData = (): FaceAmountVariesByYearTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 12500.00,
      interestRate: 0.03,
      interestAmount: 375.00,
      faceAmount: 300000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 525.00,
      loanRepayment: 600.00,
      loanOutstanding: 10425.00,
      charges: 100.00,
      netCashValue: 12550.00
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 15050.00,
      interestRate: 0.03,
      interestAmount: 451.50,
      faceAmount: 320000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 521.25,
      loanRepayment: 600.00,
      loanOutstanding: 10346.25,
      charges: 100.00,
      netCashValue: 15174.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 17674.25,
      interestRate: 0.03,
      interestAmount: 530.23,
      faceAmount: 340000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 517.31,
      loanRepayment: 600.00,
      loanOutstanding: 10263.56,
      charges: 100.00,
      netCashValue: 17875.17
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 20375.17,
      interestRate: 0.03,
      interestAmount: 611.26,
      faceAmount: 360000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 513.18,
      loanRepayment: 600.00,
      loanOutstanding: 10176.74,
      charges: 100.00,
      netCashValue: 20661.69
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 23161.69,
      interestRate: 0.03,
      interestAmount: 694.85,
      faceAmount: 380000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 508.84,
      loanRepayment: 600.00,
      loanOutstanding: 10085.58,
      charges: 100.00,
      netCashValue: 23531.12
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 45,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 26031.12,
      interestRate: 0.03,
      interestAmount: 780.93,
      faceAmount: 400000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 504.28,
      loanRepayment: 600.00,
      loanOutstanding: 9989.86,
      charges: 100.00,
      netCashValue: 26487.77
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 46,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 28987.77,
      interestRate: 0.03,
      interestAmount: 869.63,
      faceAmount: 420000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 499.49,
      loanRepayment: 600.00,
      loanOutstanding: 9889.35,
      charges: 100.00,
      netCashValue: 29532.56
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 47,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 32032.56,
      interestRate: 0.03,
      interestAmount: 960.98,
      faceAmount: 440000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 494.47,
      loanRepayment: 600.00,
      loanOutstanding: 9783.82,
      charges: 100.00,
      netCashValue: 32668.25
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 48,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 35168.25,
      interestRate: 0.03,
      interestAmount: 1055.05,
      faceAmount: 460000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 489.19,
      loanRepayment: 600.00,
      loanOutstanding: 9672.01,
      charges: 100.00,
      netCashValue: 35898.11
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 49,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 38398.11,
      interestRate: 0.03,
      interestAmount: 1151.94,
      faceAmount: 480000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 483.60,
      loanRepayment: 600.00,
      loanOutstanding: 9553.61,
      charges: 100.00,
      netCashValue: 39225.84
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 50,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 41725.84,
      interestRate: 0.03,
      interestAmount: 1251.78,
      faceAmount: 500000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 477.68,
      loanRepayment: 600.00,
      loanOutstanding: 9431.29,
      charges: 100.00,
      netCashValue: 42652.23
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 51,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 45152.23,
      interestRate: 0.03,
      interestAmount: 1354.57,
      faceAmount: 520000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 471.56,
      loanRepayment: 600.00,
      loanOutstanding: 9302.85,
      charges: 100.00,
      netCashValue: 46178.39
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 52,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 48678.39,
      interestRate: 0.03,
      interestAmount: 1460.35,
      faceAmount: 540000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 465.14,
      loanRepayment: 600.00,
      loanOutstanding: 9167.99,
      charges: 100.00,
      netCashValue: 49808.75
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 53,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 52308.75,
      interestRate: 0.03,
      interestAmount: 1569.26,
      faceAmount: 560000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 458.40,
      loanRepayment: 600.00,
      loanOutstanding: 9026.39,
      charges: 100.00,
      netCashValue: 53550.22
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 54,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 56050.22,
      interestRate: 0.03,
      interestAmount: 1681.51,
      faceAmount: 580000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 451.32,
      loanRepayment: 600.00,
      loanOutstanding: 8877.71,
      charges: 100.00,
      netCashValue: 57406.60
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 55,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 59906.60,
      interestRate: 0.03,
      interestAmount: 1797.20,
      faceAmount: 600000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 443.89,
      loanRepayment: 600.00,
      loanOutstanding: 8721.60,
      charges: 100.00,
      netCashValue: 61381.31
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 56,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 63881.31,
      interestRate: 0.03,
      interestAmount: 1916.44,
      faceAmount: 620000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 436.08,
      loanRepayment: 600.00,
      loanOutstanding: 8557.68,
      charges: 100.00,
      netCashValue: 65478.99
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 57,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 67978.99,
      interestRate: 0.03,
      interestAmount: 2039.37,
      faceAmount: 640000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 427.88,
      loanRepayment: 600.00,
      loanOutstanding: 8385.56,
      charges: 100.00,
      netCashValue: 69703.92
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 58,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 72203.92,
      interestRate: 0.03,
      interestAmount: 2166.12,
      faceAmount: 660000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 419.28,
      loanRepayment: 600.00,
      loanOutstanding: 8204.84,
      charges: 100.00,
      netCashValue: 74051.20
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 59,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 76551.20,
      interestRate: 0.03,
      interestAmount: 2296.54,
      faceAmount: 680000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 410.24,
      loanRepayment: 600.00,
      loanOutstanding: 8015.08,
      charges: 100.00,
      netCashValue: 78533.02
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 60,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 81033.02,
      interestRate: 0.03,
      interestAmount: 2430.99,
      faceAmount: 700000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 400.75,
      loanRepayment: 600.00,
      loanOutstanding: 7815.83,
      charges: 100.00,
      netCashValue: 83164.43
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 61,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 85664.43,
      interestRate: 0.03,
      interestAmount: 2569.93,
      faceAmount: 720000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 390.79,
      loanRepayment: 600.00,
      loanOutstanding: 7606.62,
      charges: 100.00,
      netCashValue: 87924.75
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 62,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 90424.75,
      interestRate: 0.03,
      interestAmount: 2712.74,
      faceAmount: 740000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 380.33,
      loanRepayment: 600.00,
      loanOutstanding: 7386.95,
      charges: 100.00,
      netCashValue: 92817.82
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 63,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 95317.82,
      interestRate: 0.03,
      interestAmount: 2859.53,
      faceAmount: 760000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 369.35,
      loanRepayment: 600.00,
      loanOutstanding: 7156.30,
      charges: 100.00,
      netCashValue: 97847.70
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 64,
      plannedPremium: 2500.00,
      netValueBeginningOfYear: 100347.70,
      interestRate: 0.03,
      interestAmount: 3010.43,
      faceAmount: 780000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 357.82,
      loanRepayment: 600.00,
      loanOutstanding: 6914.12,
      charges: 100.00,
      netCashValue: 103020.43
    }
  ];
};

export const generateRiskAssessmentTableData = (): StopPremiumAmountTableData[] => {
  return [
    {
      policyYear: 6,
      calendarYear: 2025,
      age: 40,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 12500.00,
      interestRate: 0.03,
      interestAmount: 375.00,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 12775.00
    },
    {
      policyYear: 7,
      calendarYear: 2026,
      age: 41,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 12775.00,
      interestRate: 0.03,
      interestAmount: 383.25,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 13058.25
    },
    {
      policyYear: 8,
      calendarYear: 2027,
      age: 42,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 13058.25,
      interestRate: 0.03,
      interestAmount: 391.75,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 13350.00
    },
    {
      policyYear: 9,
      calendarYear: 2028,
      age: 43,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 13350.00,
      interestRate: 0.03,
      interestAmount: 400.50,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 13650.50
    },
    {
      policyYear: 10,
      calendarYear: 2029,
      age: 44,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 13650.50,
      interestRate: 0.03,
      interestAmount: 409.52,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 13960.02
    },
    {
      policyYear: 11,
      calendarYear: 2030,
      age: 45,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 13960.02,
      interestRate: 0.03,
      interestAmount: 418.80,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 14278.82
    },
    {
      policyYear: 12,
      calendarYear: 2031,
      age: 46,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 14278.82,
      interestRate: 0.03,
      interestAmount: 428.36,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 14607.18
    },
    {
      policyYear: 13,
      calendarYear: 2032,
      age: 47,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 14607.18,
      interestRate: 0.03,
      interestAmount: 438.22,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 14945.40
    },
    {
      policyYear: 14,
      calendarYear: 2033,
      age: 48,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 14945.40,
      interestRate: 0.03,
      interestAmount: 448.36,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 15293.76
    },
    {
      policyYear: 15,
      calendarYear: 2034,
      age: 49,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 15293.76,
      interestRate: 0.03,
      interestAmount: 458.81,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 15652.57
    },
    {
      policyYear: 16,
      calendarYear: 2035,
      age: 50,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 15652.57,
      interestRate: 0.03,
      interestAmount: 469.58,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 16022.15
    },
    {
      policyYear: 17,
      calendarYear: 2036,
      age: 51,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 16022.15,
      interestRate: 0.03,
      interestAmount: 480.66,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 16402.81
    },
    {
      policyYear: 18,
      calendarYear: 2037,
      age: 52,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 16402.81,
      interestRate: 0.03,
      interestAmount: 492.08,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 16794.89
    },
    {
      policyYear: 19,
      calendarYear: 2038,
      age: 53,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 16794.89,
      interestRate: 0.03,
      interestAmount: 503.85,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 17198.74
    },
    {
      policyYear: 20,
      calendarYear: 2039,
      age: 54,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 17198.74,
      interestRate: 0.03,
      interestAmount: 515.96,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 17614.70
    },
    {
      policyYear: 21,
      calendarYear: 2040,
      age: 55,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 17614.70,
      interestRate: 0.03,
      interestAmount: 528.44,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 18043.14
    },
    {
      policyYear: 22,
      calendarYear: 2041,
      age: 56,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 18043.14,
      interestRate: 0.03,
      interestAmount: 541.29,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 18484.43
    },
    {
      policyYear: 23,
      calendarYear: 2042,
      age: 57,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 18484.43,
      interestRate: 0.03,
      interestAmount: 554.53,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 18938.96
    },
    {
      policyYear: 24,
      calendarYear: 2043,
      age: 58,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 18938.96,
      interestRate: 0.03,
      interestAmount: 568.17,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 19407.13
    },
    {
      policyYear: 25,
      calendarYear: 2044,
      age: 59,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 19407.13,
      interestRate: 0.03,
      interestAmount: 582.21,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 19889.34
    },
    {
      policyYear: 26,
      calendarYear: 2045,
      age: 60,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 19889.34,
      interestRate: 0.03,
      interestAmount: 596.68,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 20386.02
    },
    {
      policyYear: 27,
      calendarYear: 2046,
      age: 61,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 20386.02,
      interestRate: 0.03,
      interestAmount: 611.58,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 20897.60
    },
    {
      policyYear: 28,
      calendarYear: 2047,
      age: 62,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 20897.60,
      interestRate: 0.03,
      interestAmount: 626.93,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 21424.53
    },
    {
      policyYear: 29,
      calendarYear: 2048,
      age: 63,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 21424.53,
      interestRate: 0.03,
      interestAmount: 642.74,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 21967.27
    },
    {
      policyYear: 30,
      calendarYear: 2049,
      age: 64,
      plannedPremium: 0.00,
      netValueBeginningOfYear: 21967.27,
      interestRate: 0.03,
      interestAmount: 659.02,
      faceAmount: 350000.00,
      withdrawal: 0.00,
      policyLoan: 0.00,
      loanInterestRate: 0.05,
      loanInterest: 0.00,
      loanRepayment: 0.00,
      loanOutstanding: 0.00,
      charges: 100.00,
      netCashValue: 22526.29
    }
  ];
};
